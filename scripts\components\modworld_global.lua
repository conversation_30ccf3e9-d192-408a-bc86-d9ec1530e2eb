-- Component files also need GLOBAL declaration in DST when accessing global variables
-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G

local ModWorld = Class(function(self, inst)
    self.inst = inst
    self.mutators = {}
    self.current_day = -1 -- 记录当前词条对应的天数
    self.contracts = {}
    local cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    self.caravan = { next_day = cycles + ((TUNING and TUNING.CARAVAN and TUNING.CARAVAN.CARAVAN_PERIOD_DAYS) or 7) }
    self.boss_state = { deerclops = {evo = 0, kills = 0}, mactusk = {evo = 0, kills = 0} }

    -- 监听天数变化（使用官方的 cycleschanged 事件）
    inst:ListenForEvent("cycleschanged", function() self:OnNewDay() end, TheWorld)

    -- 定期检查并重新应用词条效果（每5分钟）
    inst:DoPeriodicTask(300, function()
        self:ApplyMutatorEffectsToAllPlayers()
    end)

    inst:DoTaskInTime(0, function()
        self:InitDayIfNeeded()
        self:InitContracts()
    end)
end)

function ModWorld:InitDayIfNeeded()
    if self._inited then return end
    self._inited = true

    local current_cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0

    -- 检查是否需要生成新的词条（新的一天或者没有词条）
    if self.current_day ~= current_cycles or #self.mutators == 0 then
        self.current_day = current_cycles
        self:RollMutators()
        self:AnnounceMutators()
        print("[商旅巡游录] Generated mutators for day", current_cycles)
    else
        print("[商旅巡游录] Using existing mutators for day", current_cycles)
        -- 即使使用现有词条，也要确保效果已应用到所有玩家
        self.inst:DoTaskInTime(1, function()
            self:ApplyMutatorEffectsToAllPlayers()
        end)
    end
end

function ModWorld:OnNewDay()
    local current_cycles = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    print("[商旅巡游录] New day detected:", current_cycles)

    -- 如果是新的一天，生成新词条
    if self.current_day ~= current_cycles then
        self.current_day = current_cycles
        self:RollMutators()
        self:AnnounceMutators()
        print("[商旅巡游录] Generated new mutators for day", current_cycles)
    end
end

function ModWorld:Announce(msg)
    -- 使用更安全的公告方式，避免icon_info错误
    if TheNet and TheNet.Announce then
        -- 确保消息是字符串且不为空
        local safe_msg = tostring(msg or "")
        if safe_msg ~= "" then
            -- 使用4参数格式：msg, colour, size, icon
            TheNet:Announce("[商旅巡游录] " .. safe_msg, nil, nil, nil)
        end
    end
end



-- Mutators - 饥荒风格的词条设计
local MUTATORS_POOL = {
    -- 正向词条 (有利)
    {id="bountiful_harvest", desc="丰收之日：采集额外获得1个相同物品", type="positive"},
    {id="swift_feet", desc="疾风步伐：移动速度大幅提升", type="positive"},
    {id="iron_stomach", desc="铁胃：饥饿值消耗减半", type="positive"},
    {id="clear_mind", desc="澄澈心智：理智值不会自然下降", type="positive"},
    {id="lucky_strike", desc="幸运打击：攻击有概率造成双倍伤害", type="positive"},
    {id="master_crafter", desc="工匠大师：制作物品消耗材料减半", type="positive"},
    {id="night_vision", desc="夜视：夜晚视野如同白昼", type="positive"},
    {id="beast_friend", desc="兽语者：中性生物不会主动攻击", type="positive"},

    -- 负向词条 (挑战)
    {id="fragile_tools", desc="脆弱工具：工具耐久消耗翻倍", type="negative"},
    {id="restless_night", desc="不眠之夜：夜晚理智快速流失", type="negative"},
    {id="clumsy_hands", desc="笨拙之手：有概率掉落手持物品", type="negative"},
    {id="heavy_burden", desc="负重前行：物品栏满时移动缓慢", type="negative"},
    {id="monster_magnet", desc="怪物磁铁：敌对生物更容易发现你", type="negative"},
    {id="wet_weather", desc="阴雨连绵：持续下雨", type="negative"},
    {id="cold_snap", desc="寒流来袭：温度持续下降", type="negative"},
    {id="food_spoilage", desc="腐败加速：食物腐烂速度翻倍", type="negative"},

    -- 中性词条 (有利有弊)
    {id="glass_cannon", desc="玻璃大炮：攻击力翻倍，但生命值减半", type="neutral"},
    {id="night_owl", desc="夜猫子：夜晚获得各种加成，白天虚弱", type="neutral"},
    {id="berserker", desc="狂战士：生命值越低攻击力越高", type="neutral"},
    {id="merchant_visit", desc="商人造访：今日将有特殊商队到访", type="event"},
}

function ModWorld:RollMutators()
    self.mutators = {}
    local n = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.MUTATOR_COUNT) or 2

    -- 创建可用词条池的副本
    local available_mutators = {}
    for _, mutator in ipairs(MUTATORS_POOL) do
        table.insert(available_mutators, mutator)
    end

    -- 随机选择不重复的词条
    for _ = 1, math.min(n, #available_mutators) do
        local index = math.random(#available_mutators)
        local selected = table.remove(available_mutators, index)
        table.insert(self.mutators, selected)
    end

    print("[商旅巡游录] Rolled", #self.mutators, "mutators")

    -- 应用词条效果到所有玩家
    self:ApplyMutatorEffectsToAllPlayers()
end

function ModWorld:ApplyMutatorEffectsToAllPlayers()
    -- 获取词条效果系统
    local MutatorEffects = require("systems/mutator_effects")
    if not MutatorEffects then
        print("[商旅巡游录] 错误：无法加载词条效果系统")
        return
    end

    local applied_count = 0
    local total_players = 0

    -- 应用到所有玩家
    for _, player in ipairs(GLOBAL.AllPlayers or {}) do
        total_players = total_players + 1
        -- 增强玩家有效性检查
        if player and player:IsValid() and player.components and player.userid and not player:HasTag("playerghost") then
            local success, error_msg = pcall(function()
                MutatorEffects.ApplyToPlayer(player)
            end)

            if success then
                applied_count = applied_count + 1
                print("[商旅巡游录] 成功应用词条效果到玩家:", player.userid)
            else
                print("[商旅巡游录] 应用词条效果失败:", player.userid, error_msg)
            end
        else
            print("[商旅巡游录] 跳过无效或幽灵状态玩家")
        end
    end

    print(string.format("[商旅巡游录] 词条效果应用完成: %d/%d 玩家", applied_count, total_players))
end

function ModWorld:AnnounceMutators()
    local names = {}
    for _,m in ipairs(self.mutators or {}) do
        if m and m.desc then
            table.insert(names, tostring(m.desc))
        end
    end
    if #names > 0 then
        local msg = "今日词条：" .. table.concat(names, "；")
        self:Announce(msg)
    end
end

-- Commands stubs
function ModWorld:CmdShowMutators(player)
    local parts = {"今日词条："}
    for i, m in ipairs(self.mutators) do
        local type_icon = ""
        if m.type == "positive" then type_icon = "✓"
        elseif m.type == "negative" then type_icon = "✗"
        elseif m.type == "neutral" then type_icon = "◈"
        elseif m.type == "event" then type_icon = "★"
        end
        table.insert(parts, string.format("[%d]%s%s", i, type_icon, m.desc))
    end
    local msg = table.concat(parts, " ")

    if player and player.components and player.components.talker then
        player.components.talker:Say(msg)
    else
        self:Announce(msg)
    end
end

function ModWorld:CmdRerollMutators(player)
    print("[商旅巡游录] CmdRerollMutators called by", player.userid or "unknown")

    -- 检查玩家是否有重投组件
    if not player or not player.components or not player.components.modplayer_reroll then
        if player and player.components and player.components.talker then
            player.components.talker:Say("重投系统错误")
        end
        return
    end

    local reroll_comp = player.components.modplayer_reroll

    -- 检查玩家今日是否已经重投过
    if not reroll_comp:CanReroll() then
        if player.components.talker then
            player.components.talker:Say("你今日已重掷过词条")
        end
        return
    end

    -- 使用玩家的重投次数
    if reroll_comp:UseReroll() then
        print("[商旅巡游录] Rolling new mutators...")
        self:RollMutators()
        print("[商旅巡游录] Announcing new mutators...")
        self:AnnounceMutators()
        print("[商旅巡游录] Reroll complete")

        if player.components.talker then
            player.components.talker:Say("已重掷词条！")
        end
    else
        if player.components.talker then
            player.components.talker:Say("重掷失败")
        end
    end
end

-- 合约系统
local CONTRACT_TYPES = {
    kill = {
        name = "击杀合约",
        targets = {
            {prefab = "spider", name = "蜘蛛", goal = 10, favor = 5, rep = {pig = 2}},
            {prefab = "tentacle", name = "触手", goal = 3, favor = 8, rep = {pig = 3}},
            {prefab = "hound", name = "猎犬", goal = 5, favor = 6, rep = {pig = 2}},
            {prefab = "tallbird", name = "高鸟", goal = 2, favor = 7, rep = {pig = 3}},
        }
    },
    deliver = {
        name = "交付合约",
        targets = {
            {item = "goldnugget", name = "金块", goal = 10, favor = 8, rep = {pig = 4}},
            {item = "boards", name = "木板", goal = 20, favor = 6, rep = {pig = 3}},
            {item = "cutstone", name = "石砖", goal = 15, favor = 7, rep = {pig = 3}},
            {item = "rope", name = "绳子", goal = 8, favor = 5, rep = {pig = 2}},
        }
    },
    build = {
        name = "建设合约",
        targets = {
            {prefab = "pighouse", name = "猪屋", goal = 2, favor = 15, rep = {pig = 8}},
            {prefab = "streetlamp", name = "路灯", goal = 3, favor = 12, rep = {pig = 6}},
            {prefab = "fence", name = "围墙", goal = 10, favor = 8, rep = {pig = 4}},
            {prefab = "wall_stone", name = "石墙", goal = 8, favor = 10, rep = {pig = 5}},
        }
    }
}

function ModWorld:InitContracts()
    if #self.contracts == 0 then
        self:GenerateContracts()
    end

    -- 监听相关事件
    self:ListenForContractEvents()
end

function ModWorld:GenerateContracts()
    self.contracts = {}
    local contract_count = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.CONTRACT_COUNT) or 3

    -- 从每种类型中随机选择合约
    local available_types = {"kill", "deliver", "build"}

    for i = 1, contract_count do
        local contract_type = available_types[math.random(#available_types)]
        local type_data = CONTRACT_TYPES[contract_type]
        local target_data = type_data.targets[math.random(#type_data.targets)]

        local contract = {
            id = "contract_" .. i,
            type = contract_type,
            target_data = target_data,
            progress = 0,
            goal = target_data.goal,
            reward_favor = target_data.favor,
            reward_rep = target_data.rep,
            expires = -1, -- 永不过期
            completed = false
        }

        table.insert(self.contracts, contract)
    end

    print("[商旅巡游录] 生成了", #self.contracts, "个合约")
    self:AnnounceNewContracts()
end

function ModWorld:AnnounceNewContracts()
    local msg = "新合约已发布！使用 /ccontracts 查看详情"
    self:Announce(msg)
end

function ModWorld:ListenForContractEvents()
    -- 监听玩家击杀事件（通过玩家的killed事件）
    TheWorld:ListenForEvent("ms_newplayerspawned", function(_, player)
        if player and player:IsValid() then
            -- 监听玩家击杀事件
            player:ListenForEvent("killed", function(_, data)
                if data and data.victim then
                    self:OnEntityKilled(data.victim, player)
                end
            end)

            -- 监听玩家建造事件
            player:ListenForEvent("onbuilt", function(_, data)
                if data and data.item then
                    self:OnStructureBuilt(data.item, player)
                end
            end)
        end
    end)

    -- 为已存在的玩家添加事件监听
    for _, player in ipairs(GLOBAL.AllPlayers or {}) do
        if player and player:IsValid() then
            -- 监听玩家击杀事件
            player:ListenForEvent("killed", function(_, data)
                if data and data.victim then
                    self:OnEntityKilled(data.victim, player)
                end
            end)

            -- 监听玩家建造事件
            player:ListenForEvent("onbuilt", function(_, data)
                if data and data.item then
                    self:OnStructureBuilt(data.item, player)
                end
            end)
        end
    end
end

function ModWorld:OnEntityKilled(victim, _)
    if not victim or not victim.prefab then return end

    -- 检查是否有相关的击杀合约
    for _, contract in ipairs(self.contracts) do
        if contract.type == "kill" and not contract.completed then
            if contract.target_data.prefab == victim.prefab then
                contract.progress = contract.progress + 1
                print("[商旅巡游录] 击杀合约进度:", contract.target_data.name, contract.progress .. "/" .. contract.goal)

                if contract.progress >= contract.goal then
                    self:CompleteContract(contract)
                end

                -- 广播进度更新
                local msg = string.format("合约进度：击杀%s %d/%d",
                    contract.target_data.name, contract.progress, contract.goal)
                self:Announce(msg)
                break
            end
        end
    end
end

function ModWorld:OnStructureBuilt(structure, _)
    if not structure or not structure.prefab then return end

    -- 检查是否有相关的建设合约
    for _, contract in ipairs(self.contracts) do
        if contract.type == "build" and not contract.completed then
            if contract.target_data.prefab == structure.prefab then
                contract.progress = contract.progress + 1
                print("[商旅巡游录] 建设合约进度:", contract.target_data.name, contract.progress .. "/" .. contract.goal)

                if contract.progress >= contract.goal then
                    self:CompleteContract(contract)
                end

                -- 广播进度更新
                local msg = string.format("合约进度：建造%s %d/%d",
                    contract.target_data.name, contract.progress, contract.goal)
                self:Announce(msg)
                break
            end
        end
    end
end

function ModWorld:CompleteContract(contract)
    contract.completed = true

    -- 发放奖励给所有玩家
    for _, player in ipairs(GLOBAL.AllPlayers or {}) do
        if player and player:IsValid() and player.components then
            -- 发放Favor奖励
            if player.components.modplayer_boons then
                player.components.modplayer_boons:AddFavor(contract.reward_favor)
            end

            -- 发放声望奖励
            if player.components.modplayer_rep and contract.reward_rep then
                for faction, rep_amount in pairs(contract.reward_rep) do
                    player.components.modplayer_rep:AddReputation(faction, rep_amount)
                end
            end
        end
    end

    -- 广播完成消息
            local reward_text = string.format("奖励：%d星尘", contract.reward_favor)
    if contract.reward_rep then
        for faction, rep_amount in pairs(contract.reward_rep) do
            reward_text = reward_text .. string.format("，%s声望+%d", faction, rep_amount)
        end
    end

    local msg = string.format("合约完成：%s！%s",
        contract.target_data.name, reward_text)
    self:Announce(msg)

    -- 生成新合约替换已完成的
    self:ReplaceCompletedContract(contract)
end

function ModWorld:ReplaceCompletedContract(completed_contract)
    -- 找到已完成合约的索引
    local contract_index = nil
    for i, contract in ipairs(self.contracts) do
        if contract.id == completed_contract.id then
            contract_index = i
            break
        end
    end

    if contract_index then
        -- 生成新合约
        local available_types = {"kill", "deliver", "build"}
        local contract_type = available_types[math.random(#available_types)]
        local type_data = CONTRACT_TYPES[contract_type]
        local target_data = type_data.targets[math.random(#type_data.targets)]

        local new_contract = {
            id = "contract_" .. contract_index .. "_" .. (TheWorld.state.cycles or 0),
            type = contract_type,
            target_data = target_data,
            progress = 0,
            goal = target_data.goal,
            reward_favor = target_data.favor,
            reward_rep = target_data.rep,
            expires = -1,
            completed = false
        }

        self.contracts[contract_index] = new_contract

        local msg = string.format("新合约：%s %s（目标：%d）",
            type_data.name, target_data.name, target_data.goal)
        self:Announce(msg)
    end
end

-- 交付合约处理
function ModWorld:CmdDeliverContract(player, contract_id)
    if not player or not player.components or not player.components.inventory then
        return
    end

    local contract_index = tonumber(contract_id)
    if not contract_index or contract_index < 1 or contract_index > #self.contracts then
        if player.components.talker then
            player.components.talker:Say("无效的合约编号")
        end
        return
    end

    local contract = self.contracts[contract_index]
    if not contract or contract.type ~= "deliver" or contract.completed then
        if player.components.talker then
            player.components.talker:Say("该合约无法交付或已完成")
        end
        return
    end

    local item_name = contract.target_data.item
    local needed = contract.goal - contract.progress
    local inventory = player.components.inventory

    -- 计算玩家拥有的物品数量
    local has_count = 0
    for i = 1, inventory.maxslots do
        local item = inventory.itemslots[i]
        if item and item.prefab == item_name then
            if item.components.stackable then
                has_count = has_count + item.components.stackable:StackSize()
            else
                has_count = has_count + 1
            end
        end
    end

    if has_count < needed then
        if player.components.talker then
            player.components.talker:Say(string.format("需要%s x%d，你只有%d个",
                contract.target_data.name, needed, has_count))
        end
        return
    end

    -- 移除物品
    local removed = 0
    for i = 1, inventory.maxslots do
        if removed >= needed then break end

        local item = inventory.itemslots[i]
        if item and item.prefab == item_name then
            if item.components.stackable then
                local stack_size = item.components.stackable:StackSize()
                local to_remove = math.min(stack_size, needed - removed)

                if to_remove >= stack_size then
                    inventory:RemoveItemBySlot(i)
                else
                    item.components.stackable:Get(to_remove)
                end
                removed = removed + to_remove
            else
                inventory:RemoveItemBySlot(i)
                removed = removed + 1
            end
        end
    end

    -- 更新合约进度
    contract.progress = contract.progress + removed

    if contract.progress >= contract.goal then
        self:CompleteContract(contract)
    else
        local msg = string.format("合约进度：交付%s %d/%d",
            contract.target_data.name, contract.progress, contract.goal)
        self:Announce(msg)
    end

    if player.components.talker then
        player.components.talker:Say(string.format("已交付%s x%d", contract.target_data.name, removed))
    end
end

function ModWorld:CmdShowContracts(player)
    local parts = {"当前合约："}

    if #self.contracts == 0 then
        table.insert(parts, "暂无合约")
    else
        for i, contract in ipairs(self.contracts) do
            local status = contract.completed and "[已完成]" or "[进行中]"
            local type_name = CONTRACT_TYPES[contract.type].name
            local progress_text = string.format("%d/%d", contract.progress, contract.goal)

            local extra_info = ""
            if contract.type == "deliver" and not contract.completed then
                extra_info = " (使用 /cdeliver " .. i .. " 交付)"
            end

            table.insert(parts, string.format("[%d]%s %s：%s %s%s",
                i, status, type_name, contract.target_data.name, progress_text, extra_info))
        end
    end

    local msg = table.concat(parts, " ")

    if player and player.components and player.components.talker then
        player.components.talker:Say(msg)
    else
        self:Announce(msg)
    end
end

function ModWorld:CmdShowCaravan(player)
    local day = (self.caravan and self.caravan.next_day) or 0
    local msg = string.format("预计第 %d 天出现商队", day)
    if player and player.components.talker then player.components.talker:Say(msg) else self:Announce(msg) end
end

-- Save/Load
function ModWorld:OnSave()
    return {
        mutators = self.mutators,
        current_day = self.current_day,
        contracts = self.contracts,
        caravan = self.caravan,
        boss_state = self.boss_state,
    }
end

function ModWorld:OnLoad(data)
    if data then
        self.mutators = data.mutators or self.mutators
        self.current_day = data.current_day or -1
        self.contracts = data.contracts or {}
        self.caravan = data.caravan or self.caravan
        self.boss_state = data.boss_state or self.boss_state
        print("[商旅巡游录] World data loaded, current_day:", self.current_day)
        print("[商旅巡游录] Loaded", #self.contracts, "contracts")

        -- 如果没有合约数据，初始化合约系统
        if #self.contracts == 0 then
            self:GenerateContracts()
        end
    else
        print("[商旅巡游录] No world data to load")
    end
end

return ModWorld
