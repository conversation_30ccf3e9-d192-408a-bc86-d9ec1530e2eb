-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G

local Screen = require "widgets/screen"
local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"
local ImageButton = require "widgets/imagebutton"
local TEMPLATES = require "widgets/redux/templates"

-- 安全设置颜色的小工具：支持 {r,g,b[,a]} 表，自动补 a=1
local function SetColourSafe(widget, col)
    if widget and widget.SetColour and type(col) == "table" then
        widget:SetColour(col[1] or 1, col[2] or 1, col[3] or 1, col[4] or 1)
    end
end

local CaravanScreen = Class(Screen, function(self, owner)
    Screen._ctor(self, "CaravanScreen")
    self.owner = owner

    -- 半透明背景
    self.bg = self:AddChild(ImageButton("images/global.xml", "square.tex"))
    self.bg.image:SetVRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetHRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetVAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetHAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetScaleMode(SCALEMODE_FILLSCREEN)
    self.bg.image:SetTint(0, 0, 0, 0.5)
    self.bg:SetOnClick(function() self:Close() end)
    if self.bg.SetHelpTextMessage then
        self.bg:SetHelpTextMessage("")
    end

    -- 主面板容器 - 正确设置居中
    self.root = self:AddChild(Widget("root"))
    self.root:SetHAnchor(ANCHOR_MIDDLE)
    self.root:SetVAnchor(ANCHOR_MIDDLE)
    self.root:SetPosition(0, 0)

    -- 主面板（增大尺寸，给内容更多空间）
    self.panel = self.root:AddChild(TEMPLATES.RectangleWindow(800, 700, "商旅巡游录", nil))

    -- 内容区域（更稳健）
    local body = self.panel.body or self.panel
    self.content = body:AddChild(Widget("content"))
    self.content:SetPosition(0, 0) -- 居中，整体在窗口内部布局

    -- 标签页按钮
    self.tabs = {}
    self.current_tab = "mutators"
    
    local tab_names = {
        {id = "mutators", text = "今日词条"},
        {id = "contracts", text = "合约任务"},
        {id = "boons", text = "被动恩惠"},
        {id = "reputation", text = "阵营声望"},
        {id = "caravan", text = "商队信息"}
    }
    
    -- 标签页移至底部，增大按钮尺寸
    for i, tab in ipairs(tab_names) do
        local btn = self.content:AddChild(TEMPLATES.StandardButton(
            function() self:SwitchTab(tab.id) end,
            tab.text,
            {140, 50}
        ))
        btn:SetPosition(-280 + (i-1) * 140, -280) -- 底部靠下，调整间距
        self.tabs[tab.id] = btn
    end

    -- 内容显示区域
    self.info_area = self.content:AddChild(Widget("info_area"))
    self.info_area:SetPosition(0, 0)

    -- 文本显示（使用 ScrollingTextBox 支持长文本滚动）
    local ok, ScrollingText = pcall(require, "widgets/scrollingtextbox")
    if not ok then
        -- 理论上不会失败；为了稳妥加个兜底
        ScrollingText = nil
    end

    if ScrollingText then
        -- 直接用官方滚动文本控件，增大尺寸
        self.info_text = self.info_area:AddChild(ScrollingText("", 700, 500, CHATFONT, 28))
        -- 让滚动区域居中稍向上，避免顶到边框
        self.info_text:SetPosition(0, 30)

        if self.info_text.text then
            self.info_text.text:SetHAlign(ANCHOR_LEFT)
            self.info_text.text:SetVAlign(ANCHOR_TOP)
            -- 关键：不要再改内部 text 的 RegionSize，交给 ScrollingText 自己管理
        end
    else
        -- 极端兜底：没有 ScrollingTextBox 就退化为普通 Text（没有滚动）
        self.info_text = self.info_area:AddChild(Text(CHATFONT, 28, ""))
        SetColourSafe(self.info_text, {1,1,1,1})
        self.info_text:SetRegionSize(700, 500)
        self.info_text:SetHAlign(ANCHOR_LEFT)
        self.info_text:SetVAlign(ANCHOR_TOP)
        self.info_text:SetPosition(-350, 250)
    end

    -- 操作按钮区域（紧贴底部标签之上）
    self.action_area = self.info_area:AddChild(Widget("action_area"))
    self.action_area:SetPosition(0, -200)

    -- 创建操作按钮（初始隐藏）
    self.action_buttons = {}

    self:RefreshData()
    self:SwitchTab("mutators")

    -- 设置默认焦点到第一个标签按钮，改善手柄/键盘操作体验
    self.default_focus = self.tabs["mutators"] or self.root
end)

function CaravanScreen:SwitchTab(tab_id)
    self.current_tab = tab_id

    -- 更新按钮状态
    for id, btn in pairs(self.tabs) do
        if id == tab_id then
            btn:SetTextColour(1, 1, 0, 1) -- 黄色表示选中
        else
            btn:SetTextColour(1, 1, 1, 1) -- 白色表示未选中
        end
    end

    -- 隐藏所有专用UI
    local ui_elements = {
        self.boons_ui,
        self.mutators_ui,
        self.contracts_ui,
        self.reputation_ui,
        self.caravan_ui
    }

    for _, ui in pairs(ui_elements) do
        if ui then
            ui:Hide()
        end
    end

    -- 隐藏默认文本显示（防止与专用UI重叠）
    if self.info_text then
        self.info_text:Hide()
    end

    -- 显示当前标签页的UI
    if tab_id == "boons" and self.boons_ui then
        self.boons_ui:Show()
    elseif tab_id == "mutators" and self.mutators_ui then
        self.mutators_ui:Show()
    elseif tab_id == "contracts" and self.contracts_ui then
        self.contracts_ui:Show()
    elseif tab_id == "reputation" and self.reputation_ui then
        self.reputation_ui:Show()
    elseif tab_id == "caravan" and self.caravan_ui then
        self.caravan_ui:Show()
    end

    -- 清除旧的操作按钮
    for _, btn in pairs(self.action_buttons) do
        btn:Kill()
    end
    self.action_buttons = {}

    -- 根据标签页添加相应的操作按钮
    self:CreateActionButtons(tab_id)

    self:UpdateContent()

    -- 切换标签后重置滚动到顶部
    if self.info_text then
        if self.info_text.ScrollToTop then
            self.info_text:ScrollToTop()
        elseif self.info_text.ResetView then
            self.info_text:ResetView()
        end
    end
end

function CaravanScreen:CreateActionButtons(tab_id)
    if tab_id == "mutators" then
        -- 重掷词条按钮
        local player_reroll = self.owner and self.owner.components and self.owner.components.modplayer_reroll
        local can_reroll = player_reroll and player_reroll:CanReroll()

        local reroll_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoReroll() end,
            can_reroll and "重掷词条" or "已重掷",
            {120, 40}
        ))
        reroll_btn:SetPosition(0, 0) -- 居中显示

        -- 如果不能重掷，禁用按钮
        if not can_reroll then
            reroll_btn:SetTextColour(0.5, 0.5, 0.5, 1) -- 灰色
        end

        table.insert(self.action_buttons, reroll_btn)

    elseif tab_id == "boons" then
        -- 洗点按钮
        local respec_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoRespec() end,
            "洗点重置",
            {120, 40}
        ))
        respec_btn:SetPosition(0, 0)
        table.insert(self.action_buttons, respec_btn)
    end
    -- 其他标签页暂时不需要操作按钮
end

function CaravanScreen:DoReroll()
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if world_comp then
        -- 播放标准HUD点击音效
        local s = TheFrontEnd and TheFrontEnd:GetSound()
        if s and s.PlaySound then
            s:PlaySound("dontstarve/HUD/click_move")
        end

        world_comp:CmdRerollMutators(self.owner)
        -- 延迟刷新，确保数据已更新
        self.owner:DoTaskInTime(0.1, function()
            self:RefreshData()
            self:SwitchTab("mutators") -- 重新切换到词条页面以更新按钮状态
        end)
    end
end

function CaravanScreen:DoRespec()
    -- 洗点功能
    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    if not player_boons then
        if self.owner and self.owner.components and self.owner.components.talker then
            self.owner.components.talker:Say("无法获取被动技能信息")
        end
        return
    end

    -- 计算洗点费用（已装备被动恩惠数量 * 10）
    local equipped_tbl = player_boons.equipped_boons or {}
    local equipped_count = #equipped_tbl
    local respec_cost = equipped_count * 10
    local favor = player_boons.favor or 0

    -- 如果没有装备任何被动恩惠，不需要洗点
    if equipped_count == 0 then
        if self.owner.components.talker then
            self.owner.components.talker:Say("没有装备任何被动技能，无需洗点")
        end
        return
    end

    if favor >= respec_cost then
        -- 执行洗点
        local success = player_boons:RespecAllBoons(respec_cost)
        if success then
            -- 播放标准HUD点击音效
            local s = TheFrontEnd and TheFrontEnd:GetSound()
            if s and s.PlaySound then
                s:PlaySound("dontstarve/HUD/click_move")
            end

            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✓ 洗点完成！消耗 %d 星尘（已装备的被动技能已卸下）", respec_cost))
            end
            -- 刷新UI（使用增量更新）
            if self.current_tab == "boons" then
                self:UpdateBoonsUIContent(player_boons)
            else
                self:RefreshData()
                self:SwitchTab("boons")
            end
        else
            if self.owner.components.talker then
                self.owner.components.talker:Say("✗ 洗点失败")
            end
        end
    else
        if self.owner.components.talker then
            self.owner.components.talker:Say(string.format("✗ 洗点需要 %d 星尘，当前只有 %d", respec_cost, favor))
        end
    end
end

function CaravanScreen:UpdateContent()
    local content = ""
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    local player_rep = self.owner and self.owner.components and self.owner.components.modplayer_rep

    if self.current_tab == "mutators" then
        -- 使用专用UI
        self:CreateMutatorsUI()
        return
        
    elseif self.current_tab == "contracts" then
        -- 使用专用UI
        self:CreateContractsUI()
        return
        
    elseif self.current_tab == "boons" then
        -- 被动恩惠页面现在使用新的UI布局
        self:UpdateBoonsContent()
        return
        
    elseif self.current_tab == "reputation" then
        -- 使用专用UI
        self:CreateReputationUI()
        return
        
    elseif self.current_tab == "caravan" then
        -- 使用专用UI
        self:CreateCaravanUI()
        return
    end

    self.info_text:SetString(content)

    -- 兼容不同滚动控件的安全刷新/复位
    if self.info_text then
        if self.info_text.ScrollToTop then
            self.info_text:ScrollToTop()
        elseif self.info_text.ResetView then
            self.info_text:ResetView()
        end
    end
end

-- 新的被动恩惠UI更新方法（优化版）
function CaravanScreen:UpdateBoonsContent()
    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    if not player_boons then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取被动技能信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.boons_ui then
        self:CreateBoonsUI(player_boons)
    else
        -- 如果UI已存在，只更新内容
        self:UpdateBoonsUIContent(player_boons)
    end
end

-- 创建今日词条专用UI
function CaravanScreen:CreateMutatorsUI()
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if not world_comp or not world_comp.mutators then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("暂无词条信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.mutators_ui then
        self:CreateMutatorsUIStructure()
    end

    -- 更新内容
    self:UpdateMutatorsUIContent(world_comp)
end

-- 创建今日词条UI结构
function CaravanScreen:CreateMutatorsUIStructure()
    -- 创建词条UI容器
    self.mutators_ui = self.info_area:AddChild(Widget("mutators_ui"))
    self.mutators_ui:SetPosition(0, 30)

    -- 创建标题
    self.mutators_title = self.mutators_ui:AddChild(Text(CHATFONT, 32, "今日词条"))
    SetColourSafe(self.mutators_title, {1, 1, 0, 1})
    self.mutators_title:SetPosition(0, 150)

    -- 创建词条容器
    self.mutators_container = self.mutators_ui:AddChild(Widget("mutators_container"))
    self.mutators_container:SetPosition(0, 80)

    -- 创建图例
    self:CreateMutatorsLegend()
end

-- 创建词条图例
function CaravanScreen:CreateMutatorsLegend()
    local legend_y = -140

    -- 图例标题
    local legend_title = self.mutators_ui:AddChild(Text(CHATFONT, 26, "词条类型:"))
    SetColourSafe(legend_title, {1, 1, 1, 1})
    legend_title:SetPosition(0, legend_y)

    -- 图例项目
    local legends = {
        {text = "✓ 有利词条", color = {0.2, 0.8, 0.2, 1}, pos = {-160, legend_y - 30}},
        {text = "✗ 挑战词条", color = {0.8, 0.2, 0.2, 1}, pos = {-50, legend_y - 30}},
        {text = "◈ 平衡词条", color = {0.6, 0.6, 0.6, 1}, pos = {60, legend_y - 30}},
        {text = "★ 事件词条", color = {1, 0.8, 0.2, 1}, pos = {170, legend_y - 30}}
    }

    for _, legend in ipairs(legends) do
        local legend_text = self.mutators_ui:AddChild(Text(CHATFONT, 22, legend.text))
        SetColourSafe(legend_text, legend.color)
        legend_text:SetPosition(legend.pos[1], legend.pos[2])
    end
end

-- 更新词条UI内容
function CaravanScreen:UpdateMutatorsUIContent(world_comp)
    -- 清除旧的词条
    if self.mutator_cards then
        for _, card in pairs(self.mutator_cards) do
            card:Kill()
        end
    end
    self.mutator_cards = {}

    -- 创建词条卡片
    for i, mutator in ipairs(world_comp.mutators) do
        self:CreateMutatorCard(i, mutator)
    end
end

-- 创建词条卡片
function CaravanScreen:CreateMutatorCard(index, mutator)
    local card_y = 40 - (index - 1) * 60  -- 向下移动40像素，避免盖住标题

    -- 创建卡片容器
    local card = self.mutators_container:AddChild(Widget("mutator_card_" .. index))
    card:SetPosition(0, card_y)

    -- 确定词条类型颜色
    local type_color = {0.5, 0.5, 0.5, 1} -- 默认灰色
    local type_icon = "◈"
    
    if mutator.type == "positive" then
        type_color = {0.2, 0.8, 0.2, 1} -- 绿色
        type_icon = "✓"
    elseif mutator.type == "negative" then
        type_color = {0.8, 0.2, 0.2, 1} -- 红色
        type_icon = "✗"
    elseif mutator.type == "event" then
        type_color = {1, 0.8, 0.2, 1} -- 黄色
        type_icon = "★"
    end

    -- 创建卡片背景（使用更透明的背景）
    local card_bg = card:AddChild(Image("images/global.xml", "square.tex"))
    card_bg:SetScale(1.0, 0.3)
    card_bg:SetTint(0.2, 0.2, 0.3, 0.3)  -- 更浅的背景色，更透明
    card_bg:SetPosition(0, 0)

    -- 创建词条文本
    local mutator_text = card:AddChild(Text(CHATFONT, 24, string.format("[%d] %s %s", index, type_icon, mutator.desc or "未知词条")))
    SetColourSafe(mutator_text, type_color)
    mutator_text:SetPosition(0, 0)

    -- 存储卡片引用
    self.mutator_cards[index] = card
end



-- 创建合约任务专用UI
function CaravanScreen:CreateContractsUI()
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if not world_comp or not world_comp.contracts then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取合约信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.contracts_ui then
        self:CreateContractsUIStructure()
    end

    -- 更新内容
    self:UpdateContractsUIContent(world_comp)
end

-- 创建合约任务UI结构
function CaravanScreen:CreateContractsUIStructure()
    -- 创建合约UI容器
    self.contracts_ui = self.info_area:AddChild(Widget("contracts_ui"))
    self.contracts_ui:SetPosition(0, 30)

    -- 创建标题
    self.contracts_title = self.contracts_ui:AddChild(Text(CHATFONT, 32, "合约任务"))
    SetColourSafe(self.contracts_title, {1, 1, 0, 1})
    self.contracts_title:SetPosition(0, 150)

    -- 创建状态统计
    self.contracts_stats = self.contracts_ui:AddChild(Text(CHATFONT, 22, ""))
    SetColourSafe(self.contracts_stats, {0.8, 0.8, 0.8, 1})
    self.contracts_stats:SetPosition(0, 120)

    -- 创建合约容器
    self.contracts_container = self.contracts_ui:AddChild(Widget("contracts_container"))
    self.contracts_container:SetPosition(0, 60)

    -- 创建图例
    self:CreateContractsLegend()
end

-- 创建合约图例
function CaravanScreen:CreateContractsLegend()
    local legend_y = -140

    -- 图例标题
    local legend_title = self.contracts_ui:AddChild(Text(CHATFONT, 26, "任务状态:"))
    SetColourSafe(legend_title, {1, 1, 1, 1})
    legend_title:SetPosition(0, legend_y)

    -- 图例项目
    local legends = {
        {text = "✓ 已完成", color = {0.2, 0.8, 0.2, 1}, pos = {-100, legend_y - 30}},
        {text = "○ 进行中", color = {0.8, 0.8, 0.2, 1}, pos = {100, legend_y - 30}}
    }

    for _, legend in ipairs(legends) do
        local legend_text = self.contracts_ui:AddChild(Text(CHATFONT, 22, legend.text))
        SetColourSafe(legend_text, legend.color)
        legend_text:SetPosition(legend.pos[1], legend.pos[2])
    end
end

-- 更新合约UI内容
function CaravanScreen:UpdateContractsUIContent(world_comp)
    -- 更新统计信息
    local completed_count = 0
    local total_count = #world_comp.contracts
    for _, contract in ipairs(world_comp.contracts) do
        if contract.completed then
            completed_count = completed_count + 1
        end
    end

    if self.contracts_stats then
        local stats_text = string.format("进度: %d/%d 已完成", completed_count, total_count)
        self.contracts_stats:SetString(stats_text)
    end

    -- 清除旧的合约
    if self.contract_cards then
        for _, card in pairs(self.contract_cards) do
            card:Kill()
        end
    end
    self.contract_cards = {}

    if total_count == 0 then
        -- 显示无任务信息
        local no_contracts = self.contracts_container:AddChild(Text(CHATFONT, 28, "暂无合约任务"))
        SetColourSafe(no_contracts, {0.6, 0.6, 0.6, 1})
        no_contracts:SetPosition(0, 0)
        return
    end

    -- 创建合约卡片
    for i, contract in ipairs(world_comp.contracts) do
        self:CreateContractCard(i, contract)
    end
end

-- 创建合约卡片
function CaravanScreen:CreateContractCard(index, contract)
    local card_y = 40 - (index - 1) * 85  -- 调整间距和起始位置

    -- 创建卡片容器
    local card = self.contracts_container:AddChild(Widget("contract_card_" .. index))
    card:SetPosition(0, card_y)

    -- 确定状态颜色
    local status_color = {0.8, 0.8, 0.2, 1} -- 默认黄色（进行中）
    local status_icon = "○"

    if contract.completed then
        status_color = {0.2, 0.8, 0.2, 1} -- 绿色（已完成）
        status_icon = "✓"
    end

    -- 创建卡片背景（根据完成状态调整颜色）
    local card_bg = card:AddChild(Image("images/global.xml", "square.tex"))
    card_bg:SetScale(1.2, 0.45)  -- 稍微加大卡片
    if contract.completed then
        card_bg:SetTint(0.1, 0.3, 0.1, 0.4)  -- 完成的任务用绿色背景
    else
        card_bg:SetTint(0.2, 0.2, 0.3, 0.3)  -- 进行中的任务用蓝色背景
    end
    card_bg:SetPosition(0, 0)

    -- 获取任务类型名称
    local type_name = ""
    if contract.type == "kill" then
        type_name = "击杀"
    elseif contract.type == "deliver" then
        type_name = "交付"
    elseif contract.type == "build" then
        type_name = "建设"
    end

    -- 创建任务标题
    local target_name = (contract.target_data and contract.target_data.name) or "未知"
    local title_text = string.format("[%d] %s %s：%s", index, status_icon, type_name, target_name)
    local title = card:AddChild(Text(CHATFONT, 22, title_text))
    SetColourSafe(title, status_color)
    title:SetPosition(0, 10)

    -- 创建进度文本
    local progress_text = string.format("进度: %d/%d", contract.progress or 0, contract.goal or 0)
    local progress = card:AddChild(Text(CHATFONT, 18, progress_text))
    SetColourSafe(progress, {0.8, 0.8, 0.8, 1})
    progress:SetPosition(-150, -10)

    -- 创建奖励文本
    local reward_text = string.format("奖励: %d星尘", contract.reward_favor or 0)
    if contract.reward_rep then
        for faction, rep_amount in pairs(contract.reward_rep) do
            reward_text = reward_text .. string.format("，%s声望+%d", faction, rep_amount)
        end
    end
    local reward = card:AddChild(Text(CHATFONT, 18, reward_text))
    SetColourSafe(reward, {0.8, 0.8, 0.8, 1})
    reward:SetPosition(150, -10)

    -- 如果是交付合约且未完成，显示交付提示
    if contract.type == "deliver" and not contract.completed then
        local deliver_hint = card:AddChild(Text(CHATFONT, 16, string.format("使用 cdeliver %d 交付物品", index)))
        SetColourSafe(deliver_hint, {0.6, 0.8, 1, 1})
        deliver_hint:SetPosition(0, -25)
    end

    -- 存储卡片引用
    self.contract_cards[index] = card
end

-- 创建阵营声望专用UI
function CaravanScreen:CreateReputationUI()
    local player_rep = self.owner and self.owner.components and self.owner.components.modplayer_rep
    if not player_rep or not player_rep.reps then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取声望信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.reputation_ui then
        self:CreateReputationUIStructure()
    end

    -- 更新内容
    self:UpdateReputationUIContent(player_rep)
end

-- 创建阵营声望UI结构
function CaravanScreen:CreateReputationUIStructure()
    -- 创建声望UI容器
    self.reputation_ui = self.info_area:AddChild(Widget("reputation_ui"))
    self.reputation_ui:SetPosition(0, 30)

    -- 创建标题
    self.reputation_title = self.reputation_ui:AddChild(Text(CHATFONT, 32, "阵营声望"))
    SetColourSafe(self.reputation_title, {1, 1, 0, 1})
    self.reputation_title:SetPosition(0, 150)

    -- 创建说明文本
    self.reputation_desc = self.reputation_ui:AddChild(Text(CHATFONT, 20, "声望影响交易折扣、护航协助和隐藏商品"))
    SetColourSafe(self.reputation_desc, {0.8, 0.8, 0.8, 1})
    self.reputation_desc:SetPosition(0, 120)

    -- 创建声望容器
    self.reputation_container = self.reputation_ui:AddChild(Widget("reputation_container"))
    self.reputation_container:SetPosition(0, 60)

    -- 创建声望条存储
    self.reputation_bars = {}

    -- 创建效果说明
    self:CreateReputationEffects()
end

-- 创建声望效果说明
function CaravanScreen:CreateReputationEffects()
    local effects_y = -140

    -- 效果标题
    local effects_title = self.reputation_ui:AddChild(Text(CHATFONT, 26, "声望效果:"))
    SetColourSafe(effects_title, {1, 1, 1, 1})
    effects_title:SetPosition(0, effects_y)

    -- 效果列表
    local effects = {
        {text = "• 交易折扣", pos = {-120, effects_y - 30}},
        {text = "• 护航协助", pos = {0, effects_y - 30}},
        {text = "• 隐藏商品", pos = {120, effects_y - 30}}
    }

    for _, effect in ipairs(effects) do
        local effect_text = self.reputation_ui:AddChild(Text(CHATFONT, 22, effect.text))
        SetColourSafe(effect_text, {0.8, 0.8, 0.8, 1})
        effect_text:SetPosition(effect.pos[1], effect.pos[2])
    end
end

-- 更新声望UI内容
function CaravanScreen:UpdateReputationUIContent(player_rep)
    -- 清除旧的声望条
    if self.reputation_bars then
        for _, bar in pairs(self.reputation_bars) do
            bar:Kill()
        end
    end
    self.reputation_bars = {}

    -- 定义阵营信息
    local factions = {
        {name = "猪人阵营", key = "pig", color = {0.8, 0.6, 0.4, 1}},
        {name = "猫狸阵营", key = "cat", color = {0.4, 0.8, 0.8, 1}},
        {name = "兔人阵营", key = "bunny", color = {0.8, 0.4, 0.8, 1}}
    }

    -- 创建声望条
    for i, faction in ipairs(factions) do
        self:CreateReputationBar(i, faction, player_rep.reps[faction.key] or 0)
    end
end

-- 创建声望条
function CaravanScreen:CreateReputationBar(index, faction, reputation_value)
    local bar_y = 40 - (index - 1) * 60  -- 调整间距

    -- 创建声望条容器
    local bar_container = self.reputation_container:AddChild(Widget("reputation_bar_" .. faction.key))
    bar_container:SetPosition(0, bar_y)

    -- 创建阵营图标和名称
    local faction_name = bar_container:AddChild(Text(CHATFONT, 26, "● " .. faction.name))
    SetColourSafe(faction_name, faction.color)
    faction_name:SetPosition(-180, 0)

    -- 创建声望值和等级
    local max_reputation = GLOBAL.TUNING.CARAVAN.MAX_REPUTATION or 1000
    local level = math.floor(reputation_value / 100) + 1  -- 每100声望一级
    local reputation_text = bar_container:AddChild(Text(CHATFONT, 22, string.format("Lv.%d (%d)", level, reputation_value)))
    SetColourSafe(reputation_text, {1, 1, 1, 1})
    reputation_text:SetPosition(180, 0)

    -- 创建进度条背景
    local bar_bg = bar_container:AddChild(Image("images/global.xml", "square.tex"))
    bar_bg:SetScale(0.8, 0.2)
    bar_bg:SetTint(0.3, 0.3, 0.4, 0.6)  -- 更浅的背景色，更透明
    bar_bg:SetPosition(0, 0)

    -- 创建进度条填充（根据声望值计算）
    local fill_ratio = math.min(reputation_value / max_reputation, 1.0)
    
    if fill_ratio > 0 then
        local bar_fill = bar_container:AddChild(Image("images/global.xml", "square.tex"))
        bar_fill:SetScale(0.8 * fill_ratio, 0.2)
        bar_fill:SetTint(faction.color[1], faction.color[2], faction.color[3], 0.8)
        bar_fill:SetPosition(-0.4 * (1 - fill_ratio), 0) -- 左对齐
    end

    -- 存储声望条引用
    self.reputation_bars[faction.key] = bar_container
end

-- 创建商队信息专用UI
function CaravanScreen:CreateCaravanUI()
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if not world_comp or not world_comp.caravan then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取商队信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.caravan_ui then
        self:CreateCaravanUIStructure()
    end

    -- 更新内容
    self:UpdateCaravanUIContent(world_comp)
end

-- 创建商队信息UI结构
function CaravanScreen:CreateCaravanUIStructure()
    -- 创建商队UI容器
    self.caravan_ui = self.info_area:AddChild(Widget("caravan_ui"))
    self.caravan_ui:SetPosition(0, 30)

    -- 创建标题
    self.caravan_title = self.caravan_ui:AddChild(Text(CHATFONT, 32, "商队信息"))
    SetColourSafe(self.caravan_title, {1, 1, 0, 1})
    self.caravan_title:SetPosition(0, 200)

    -- 创建信息容器
    self.caravan_container = self.caravan_ui:AddChild(Widget("caravan_container"))
    self.caravan_container:SetPosition(0, 120)

    -- 创建功能说明
    self:CreateCaravanFunctions()
end

-- 创建商队功能说明
function CaravanScreen:CreateCaravanFunctions()
    local functions_y = -140

    -- 功能标题
    local functions_title = self.caravan_ui:AddChild(Text(CHATFONT, 26, "商队功能:"))
    SetColourSafe(functions_title, {1, 1, 1, 1})
    functions_title:SetPosition(0, functions_y)

    -- 功能列表
    local functions = {
        {text = "• 回收冗余资源", pos = {-140, functions_y - 30}},
        {text = "• 兑换星尘代币", pos = {0, functions_y - 30}},
        {text = "• 获取稀有蓝图", pos = {140, functions_y - 30}}
    }

    for _, func in ipairs(functions) do
        local func_text = self.caravan_ui:AddChild(Text(CHATFONT, 22, func.text))
        SetColourSafe(func_text, {0.8, 0.8, 0.8, 1})
        func_text:SetPosition(func.pos[1], func.pos[2])
    end
end

-- 更新商队UI内容
function CaravanScreen:UpdateCaravanUIContent(world_comp)
    -- 清除旧的信息
    if self.caravan_info then
        for _, info in pairs(self.caravan_info) do
            info:Kill()
        end
    end
    self.caravan_info = {}

    local next_day = world_comp.caravan.next_day or 0
    local current_day = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    local days_remaining = next_day - current_day

    -- 创建时间信息
    self:CreateCaravanTimeInfo(next_day, current_day, days_remaining)
end

-- 创建商队时间信息
function CaravanScreen:CreateCaravanTimeInfo(next_day, current_day, days_remaining)
    -- 下次商队时间
    local next_caravan = self.caravan_container:AddChild(Text(CHATFONT, 26, string.format("下次商队: 第 %d 天", next_day)))
    SetColourSafe(next_caravan, {0.8, 0.8, 0.2, 1})
    next_caravan:SetPosition(0, 60)
    table.insert(self.caravan_info, next_caravan)

    -- 当前天数
    local current_day_text = self.caravan_container:AddChild(Text(CHATFONT, 26, string.format("当前天数: 第 %d 天", current_day)))
    SetColourSafe(current_day_text, {0.8, 0.8, 0.8, 1})
    current_day_text:SetPosition(0, 20)
    table.insert(self.caravan_info, current_day_text)

    -- 状态信息
    local status_text = ""
    local status_color = {1, 1, 1, 1}

    if days_remaining <= 0 then
        status_text = "商队应该已经到达！"
        status_color = {0.2, 0.8, 0.2, 1} -- 绿色
    else
        status_text = string.format("还需等待 %d 天", days_remaining)
        status_color = {0.8, 0.8, 0.2, 1} -- 黄色
    end

    local status = self.caravan_container:AddChild(Text(CHATFONT, 28, status_text))
    SetColourSafe(status, status_color)
    status:SetPosition(0, -20)
    table.insert(self.caravan_info, status)

    -- 创建倒计时条（如果商队还没到）
    if days_remaining > 0 then
        self:CreateCountdownBar(days_remaining)
    end
end

-- 创建倒计时条
function CaravanScreen:CreateCountdownBar(days_remaining)
    local bar_y = -80

    -- 倒计时条背景
    local countdown_bg = self.caravan_container:AddChild(Image("images/global.xml", "square.tex"))
    countdown_bg:SetScale(1.0, 0.25)
    countdown_bg:SetTint(0.3, 0.3, 0.4, 0.6)  -- 更浅的背景色，更透明
    countdown_bg:SetPosition(0, bar_y)
    table.insert(self.caravan_info, countdown_bg)

    -- 倒计时条填充
    local max_wait_days = GLOBAL.TUNING.CARAVAN.MAX_CARAVAN_WAIT_DAYS
    local fill_ratio = math.max(0, 1 - (days_remaining / max_wait_days))
    
    if fill_ratio > 0 then
        local countdown_fill = self.caravan_container:AddChild(Image("images/global.xml", "square.tex"))
        countdown_fill:SetScale(1.0 * fill_ratio, 0.25)
        countdown_fill:SetTint(0.2, 0.8, 0.2, 0.8)
        countdown_fill:SetPosition(-0.5 * (1 - fill_ratio), bar_y) -- 左对齐
        table.insert(self.caravan_info, countdown_fill)
    end

    -- 倒计时文本
    local countdown_text = self.caravan_container:AddChild(Text(CHATFONT, 22, string.format("倒计时: %d 天", days_remaining)))
    SetColourSafe(countdown_text, {1, 1, 1, 1})
    countdown_text:SetPosition(0, bar_y)
    table.insert(self.caravan_info, countdown_text)
end

-- 创建被动恩惠UI结构（只在第一次调用）
function CaravanScreen:CreateBoonsUI(player_boons)
    -- 创建被动恩惠UI容器
    self.boons_ui = self.info_area:AddChild(Widget("boons_ui"))
    self.boons_ui:SetPosition(0, 30)

    -- 创建头部信息区域
    self.boons_header = self.boons_ui:AddChild(Text(CHATFONT, 30, ""))
    SetColourSafe(self.boons_header, {1, 1, 0, 1})
    self.boons_header:SetPosition(0, 200)

    -- 添加操作说明
    local help_text = "鼠标悬停查看详情，点击进行操作"
    local help = self.boons_ui:AddChild(Text(CHATFONT, 24, help_text))
    SetColourSafe(help, {0.8, 0.8, 0.8, 1})
    help:SetPosition(0, 160)

    -- 创建技能树布局
    self:CreateSkillTree(player_boons)

    -- 初始更新内容
    self:UpdateBoonsUIContent(player_boons)
end

-- 更新被动恩惠UI内容（增量更新）
function CaravanScreen:UpdateBoonsUIContent(player_boons)
    -- 更新头部信息
    if self.boons_header then
        local favor = player_boons.favor or 0
        local equipped_tbl = player_boons.equipped_boons or {}
        local max_equipped = player_boons.max_equipped or 2
        local header_text = string.format("星尘: %d    已装备: %d/%d",
            favor, #equipped_tbl, max_equipped)
        self.boons_header:SetString(header_text)
    end

    -- 更新所有按钮状态
    if self.boon_buttons then
        for boon_id, button_data in pairs(self.boon_buttons) do
            self:UpdateBoonButtonState(boon_id, button_data, player_boons)
        end
    end
end

-- 创建技能树布局（优化版）
function CaravanScreen:CreateSkillTree(player_boons)
    local available_boons = player_boons:GetAvailableBoons()

    -- 初始化按钮管理器
    self.boon_buttons = {}

    -- 定义职业分类和位置（调整位置让布局更合理）
    local categories = {
        {name = "战士系", category = "warrior", pos = {-160, 60}, color = {1, 0.3, 0.3}},
        {name = "法师系", category = "mage", pos = {160, 60}, color = {0.3, 0.3, 1}},
        {name = "召唤师系", category = "summoner", pos = {-160, -100}, color = {0.8, 0.3, 1}},
        {name = "农民系", category = "farmer", pos = {160, -100}, color = {0.3, 1, 0.3}}
    }

    for _, cat_info in ipairs(categories) do
        self:CreateCategorySection(cat_info, available_boons, player_boons)
    end
end



-- 创建职业分类区域
function CaravanScreen:CreateCategorySection(cat_info, available_boons, player_boons)
    -- 职业标题
    local title = self.boons_ui:AddChild(Text(CHATFONT, 28, cat_info.name))
    SetColourSafe(title, { (cat_info.color and cat_info.color[1]) or 1,
                           (cat_info.color and cat_info.color[2]) or 1,
                           (cat_info.color and cat_info.color[3]) or 1, 1 })
    title:SetPosition(cat_info.pos[1], cat_info.pos[2] + 60)

    -- 获取该职业的被动恩惠
    local category_boons = {}
    for boon_id, boon_def in pairs(available_boons) do
        if boon_def.category == cat_info.category then
            table.insert(category_boons, {id = boon_id, def = boon_def})
        end
    end

    -- 按费用排序
    table.sort(category_boons, function(a, b) return a.def.cost < b.def.cost end)

    -- 创建被动恩惠按钮
    for i, boon_info in ipairs(category_boons) do
        local y_offset = cat_info.pos[2] + 20 - (i - 1) * 45
        self:CreateBoonButton(boon_info.id, boon_info.def, cat_info.pos[1], y_offset, player_boons)
    end
end

-- 创建被动恩惠按钮（优化版）
function CaravanScreen:CreateBoonButton(boon_id, boon_def, x, y, player_boons)
    -- 创建按钮容器
    local button_container = self.boons_ui:AddChild(Widget("boon_button_" .. boon_id))
    button_container:SetPosition(x, y)

    -- 创建按钮
    local button = button_container:AddChild(TEMPLATES.StandardButton(
        function() self:OnBoonButtonClick(boon_id, boon_def, player_boons) end,
        boon_def.name,
        {180, 40}
    ))

    -- 添加Focus事件处理（饥荒UI标准，链式调用保留原有行为）
    local old_gain = button.OnGainFocus
    button.OnGainFocus = function(btn)
        if old_gain then old_gain(btn) end
        if btn.image then
            btn.image:SetTint(1, 1, 1, 1) -- 直接拉满
        end
    end

    local old_lose = button.OnLoseFocus
    button.OnLoseFocus = function(btn)
        if old_lose then old_lose(btn) end
        -- 恢复原始颜色（在UpdateBoonButtonState中设置）
        local button_data = self.boon_buttons[boon_id]
        if button_data then
            self:UpdateBoonButtonState(boon_id, button_data, player_boons)
        end
    end

    -- 存储按钮数据以便后续更新
    local button_data = {
        container = button_container,
        button = button,
        boon_def = boon_def
    }
    self.boon_buttons[boon_id] = button_data

    -- 初始化按钮状态
    self:UpdateBoonButtonState(boon_id, button_data, player_boons)

    return button_container
end

-- 更新单个按钮状态（增量更新）
function CaravanScreen:UpdateBoonButtonState(boon_id, button_data, player_boons)
    local boon_def = button_data.boon_def
    local button = button_data.button

    -- 安全的表引用，避免索引nil
    local unlocked_tbl = player_boons.unlocked_boons or {}
    local equipped_tbl = player_boons.equipped_boons or {}

    -- 检查状态
    local is_unlocked = unlocked_tbl[boon_id] and unlocked_tbl[boon_id] > 0
    local is_equipped = false
    for _, equipped_id in ipairs(equipped_tbl) do
        if equipped_id == boon_id then
            is_equipped = true
            break
        end
    end

    -- 确定按钮颜色和文本
    local button_color = {0.5, 0.5, 0.5, 1} -- 默认灰色
    local text_color = {1, 1, 1, 1}
    local button_text = boon_def.name
    local status_text = ""

    if is_equipped then
        button_color = {0.2, 0.8, 0.2, 1} -- 绿色（已装备）
        text_color = {1, 1, 1, 1}
        button_text = "✓ " .. boon_def.name
        status_text = "已装备"
    elseif is_unlocked then
        button_color = {0.8, 0.8, 0.2, 1} -- 黄色（已解锁）
        text_color = {0, 0, 0, 1}
        status_text = "已解锁"
    else
        button_color = {0.3, 0.3, 0.3, 1} -- 深灰色（未解锁）
        text_color = {0.7, 0.7, 0.7, 1}
        status_text = "未解锁"
    end

    -- 更新按钮外观
    if button.image then
        button.image:SetTint(button_color[1], button_color[2], button_color[3], button_color[4])
    end
    if button.text then
        button.text:SetColour(text_color[1], text_color[2], text_color[3], text_color[4])
        button:SetText(button_text)
    end

    -- 更新tooltip（添加desc安全检查）
    local desc = boon_def.desc or "暂无描述"
    local tooltip_text = string.format("%s\n\n%s\n\n费用: %d 星尘\n状态: %s",
        boon_def.name, desc, boon_def.cost, status_text)

    if is_equipped then
        tooltip_text = tooltip_text .. "\n\n点击卸下此被动技能"
    elseif is_unlocked then
        tooltip_text = tooltip_text .. "\n\n点击装备此被动技能"
        local max_equipped = player_boons.max_equipped or 2
        if #equipped_tbl >= max_equipped then
            tooltip_text = tooltip_text .. "\n注意: 装备槽已满"
        end
    else
        tooltip_text = tooltip_text .. "\n\n点击解锁此被动技能"
        local favor = player_boons.favor or 0
        if favor < boon_def.cost then
            tooltip_text = tooltip_text .. "\n注意: 星尘不足"
        end
    end

    button:SetHoverText(tooltip_text)
end

-- 处理被动恩惠按钮点击（优化版）
function CaravanScreen:OnBoonButtonClick(boon_id, boon_def, player_boons)
    -- 安全的表引用，避免索引nil
    local unlocked_tbl = player_boons.unlocked_boons or {}
    local equipped_tbl = player_boons.equipped_boons or {}

    local is_unlocked = unlocked_tbl[boon_id] and unlocked_tbl[boon_id] > 0
    local is_equipped = false
    for _, equipped_id in ipairs(equipped_tbl) do
        if equipped_id == boon_id then
            is_equipped = true
            break
        end
    end

    local success = false

    if is_equipped then
        -- 卸下被动恩惠（使用组件自带的消息）
        success = player_boons:UnequipBoon(boon_id, false)
    elseif is_unlocked then
        -- 装备被动恩惠
        local max_equipped = player_boons.max_equipped or 2
        if #equipped_tbl >= max_equipped then
            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✗ 装备槽已满 (%d/%d)，请先卸下其他被动技能", #equipped_tbl, max_equipped))
            end
            return
        else
            success = player_boons:EquipBoon(boon_id, false)
        end
    else
        -- 解锁被动恩惠
        local favor = player_boons.favor or 0
        if favor >= boon_def.cost then
            success = player_boons:UnlockBoon(boon_id, false)
        else
            local needed = boon_def.cost - favor
            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✗ 星尘不足！需要 %d 星尘，还差 %d", boon_def.cost, needed))
            end
            return
        end
    end

    -- 只有成功操作才更新UI（使用增量更新）
    if success then
        self:UpdateBoonsUIContent(player_boons)
    end
end

function CaravanScreen:RefreshData()
    self:UpdateContent()
end

function CaravanScreen:Close()
    TheFrontEnd:PopScreen()
end

function CaravanScreen:OnControl(control, down)
    if CaravanScreen._base.OnControl(self, control, down) then
        return true
    end

    if not down and (control == CONTROL_CANCEL or control == CONTROL_MAP) then
        -- 播放关闭音效
        if TheFrontEnd and TheFrontEnd.GetSound and TheFrontEnd:GetSound() then
            local sound = TheFrontEnd:GetSound()
            if sound and sound.PlaySound then
                sound:PlaySound("dontstarve/HUD/click_move")
            end
        end
        self:Close()
        return true
    end

    return false
end

function CaravanScreen:GetHelpText()
    local controller_id = TheInput:GetControllerID()
    local t = {}
    table.insert(t, TheInput:GetLocalizedControl(controller_id, CONTROL_CANCEL) .. " " .. STRINGS.UI.HELP.BACK)
    return table.concat(t, "  ")
end

return CaravanScreen
