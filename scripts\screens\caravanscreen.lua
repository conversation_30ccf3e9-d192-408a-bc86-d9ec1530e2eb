local Screen = require "widgets/screen"
local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"
local ImageButton = require "widgets/imagebutton"
local TEMPLATES = require "widgets/redux/templates"

-- 安全设置颜色的小工具：支持 {r,g,b[,a]} 表，自动补 a=1
local function SetColourSafe(widget, col)
    if widget and widget.SetColour and type(col) == "table" then
        widget:SetColour(col[1] or 1, col[2] or 1, col[3] or 1, col[4] or 1)
    end
end

local CaravanScreen = Class(Screen, function(self, owner)
    Screen._ctor(self, "CaravanScreen")
    self.owner = owner

    -- 半透明背景
    self.bg = self:AddChild(ImageButton("images/global.xml", "square.tex"))
    self.bg.image:SetVRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetHRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetVAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetHAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetScaleMode(SCALEMODE_FILLSCREEN)
    self.bg.image:SetTint(0, 0, 0, 0.5)
    self.bg:SetOnClick(function() self:Close() end)
    self.bg:SetHelpTextMessage("")

    -- 主面板容器 - 正确设置居中
    self.root = self:AddChild(Widget("root"))
    self.root:SetScaleMode(SCALEMODE_PROPORTIONAL)
    self.root:SetHAnchor(ANCHOR_MIDDLE)
    self.root:SetVAnchor(ANCHOR_MIDDLE)
    self.root:SetPosition(0, 0)

    -- 主面板（移除底部按钮，留出更多空间）
    self.panel = self.root:AddChild(TEMPLATES.RectangleWindow(600, 500, "商旅巡游录", nil))

    -- 内容区域（更稳健）
    local body = self.panel.body or self.panel
    self.content = body:AddChild(Widget("content"))
    self.content:SetPosition(0, 0) -- 居中，整体在窗口内部布局

    -- 标签页按钮
    self.tabs = {}
    self.current_tab = "mutators"
    
    local tab_names = {
        {id = "mutators", text = "今日词条"},
        {id = "contracts", text = "合约任务"},
        {id = "boons", text = "被动恩惠"},
        {id = "reputation", text = "阵营声望"},
        {id = "caravan", text = "商队信息"}
    }
    
    -- 标签页移至底部
    for i, tab in ipairs(tab_names) do
        local btn = self.content:AddChild(TEMPLATES.StandardButton(
            function() self:SwitchTab(tab.id) end,
            tab.text,
            {120, 40}
        ))
        btn:SetPosition(-200 + (i-1) * 100, -200) -- 底部靠下
        self.tabs[tab.id] = btn
    end

    -- 内容显示区域
    self.info_area = self.content:AddChild(Widget("info_area"))
    self.info_area:SetPosition(0, 0)

    -- 文本显示（使用 ScrollingTextBox 支持长文本滚动）
    local ok, ScrollingText = pcall(require, "widgets/scrollingtextbox")
    if not ok then
        -- 理论上不会失败；为了稳妥加个兜底
        ScrollingText = nil
    end

    if ScrollingText then
        -- 直接用官方滚动文本控件
        self.info_text = self.info_area:AddChild(ScrollingText("", 520, 320))
        -- 让滚动区域居中稍向上，避免顶到边框；不再采用"大负偏移"
        self.info_text:SetPosition(0, 20)

        if self.info_text.text then
            self.info_text.text:SetFont(CHATFONT)
            self.info_text.text:SetSize(24)
            self.info_text.text:SetHAlign(ANCHOR_LEFT)
            self.info_text.text:SetVAlign(ANCHOR_TOP)
            -- 关键：不要再改内部 text 的 RegionSize，交给 ScrollingText 自己管理
            -- self.info_text.text:SetRegionSize(480, 1000)  -- 删除
        end
    else
        -- 极端兜底：没有 ScrollingTextBox 就退化为普通 Text（没有滚动）
        self.info_text = self.info_area:AddChild(Text(CHATFONT, 24, ""))
        SetColourSafe(self.info_text, {1,1,1,1})
        self.info_text:SetRegionSize(500, 300)
        self.info_text:SetHAlign(ANCHOR_LEFT)
        self.info_text:SetVAlign(ANCHOR_TOP)
        self.info_text:SetPosition(-250, 150)
    end

    -- 操作按钮区域（紧贴底部标签之上）
    self.action_area = self.info_area:AddChild(Widget("action_area"))
    self.action_area:SetPosition(0, -150)

    -- 创建操作按钮（初始隐藏）
    self.action_buttons = {}

    self:RefreshData()
    self:SwitchTab("mutators")

    -- 设置默认焦点到第一个标签按钮，改善手柄/键盘操作体验
    self.default_focus = self.tabs["mutators"] or self.root
end)

function CaravanScreen:SwitchTab(tab_id)
    self.current_tab = tab_id

    -- 更新按钮状态
    for id, btn in pairs(self.tabs) do
        if id == tab_id then
            btn:SetTextColour(1, 1, 0, 1) -- 黄色表示选中
        else
            btn:SetTextColour(1, 1, 1, 1) -- 白色表示未选中
        end
    end

    -- 显示/隐藏被动恩惠专用UI
    if self.boons_ui ~= nil then
        if tab_id == "boons" then
            self.boons_ui:Show()
        else
            self.boons_ui:Hide()
        end
    end

    -- 清除旧的操作按钮
    for _, btn in pairs(self.action_buttons) do
        btn:Kill()
    end
    self.action_buttons = {}

    -- 根据标签页添加相应的操作按钮
    self:CreateActionButtons(tab_id)

    self:UpdateContent()

    -- 切换标签后重置滚动到顶部
    if self.info_text then
        if self.info_text.ScrollToTop then
            self.info_text:ScrollToTop()
        elseif self.info_text.ResetView then
            self.info_text:ResetView()
        end
    end
end

function CaravanScreen:CreateActionButtons(tab_id)
    if tab_id == "mutators" then
        -- 重掷词条按钮
        local player_reroll = self.owner and self.owner.components and self.owner.components.modplayer_reroll
        local can_reroll = player_reroll and player_reroll:CanReroll()

        local reroll_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoReroll() end,
            can_reroll and "重掷词条" or "已重掷",
            {120, 40}
        ))
        reroll_btn:SetPosition(0, 0) -- 居中显示

        -- 如果不能重掷，禁用按钮
        if not can_reroll then
            reroll_btn:SetTextColour(0.5, 0.5, 0.5, 1) -- 灰色
        end

        table.insert(self.action_buttons, reroll_btn)

    elseif tab_id == "boons" then
        -- 洗点按钮
        local respec_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoRespec() end,
            "洗点重置",
            {120, 40}
        ))
        respec_btn:SetPosition(0, 0)
        table.insert(self.action_buttons, respec_btn)
    end
    -- 其他标签页暂时不需要操作按钮
end

function CaravanScreen:DoReroll()
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if world_comp then
        -- 播放标准HUD点击音效
        local s = TheFrontEnd and TheFrontEnd:GetSound()
        if s and s.PlaySound then
            s:PlaySound("dontstarve/HUD/click_move")
        end

        world_comp:CmdRerollMutators(self.owner)
        -- 延迟刷新，确保数据已更新
        self.owner:DoTaskInTime(0.1, function()
            self:RefreshData()
            self:SwitchTab("mutators") -- 重新切换到词条页面以更新按钮状态
        end)
    end
end

function CaravanScreen:DoRespec()
    -- 洗点功能
    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    if not player_boons then
        if self.owner and self.owner.components and self.owner.components.talker then
            self.owner.components.talker:Say("无法获取被动恩惠信息")
        end
        return
    end

    -- 计算洗点费用（已装备被动恩惠数量 * 10）
    local equipped_tbl = player_boons.equipped_boons or {}
    local equipped_count = #equipped_tbl
    local respec_cost = equipped_count * 10
    local favor = player_boons.favor or 0

    -- 如果没有装备任何被动恩惠，不需要洗点
    if equipped_count == 0 then
        if self.owner.components.talker then
            self.owner.components.talker:Say("没有装备任何被动恩惠，无需洗点")
        end
        return
    end

    if favor >= respec_cost then
        -- 执行洗点
        local success = player_boons:RespecAllBoons(respec_cost)
        if success then
            -- 播放标准HUD点击音效
            local s = TheFrontEnd and TheFrontEnd:GetSound()
            if s and s.PlaySound then
                s:PlaySound("dontstarve/HUD/click_move")
            end

            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✓ 洗点完成！消耗 %d 恩惠", respec_cost))
            end
            -- 刷新UI（使用增量更新）
            if self.current_tab == "boons" then
                self:UpdateBoonsUIContent(player_boons)
            else
                self:RefreshData()
                self:SwitchTab("boons")
            end
        else
            if self.owner.components.talker then
                self.owner.components.talker:Say("✗ 洗点失败")
            end
        end
    else
        if self.owner.components.talker then
            self.owner.components.talker:Say(string.format("✗ 洗点需要 %d 恩惠，当前只有 %d", respec_cost, favor))
        end
    end
end

function CaravanScreen:UpdateContent()
    local content = ""
    local world_comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    local player_rep = self.owner and self.owner.components and self.owner.components.modplayer_rep

    if self.current_tab == "mutators" then
        content = "=== 今日词条 ===\n\n"
        if world_comp and world_comp.mutators then
            for i, mutator in ipairs(world_comp.mutators) do
                local type_icon = ""
                if mutator.type == "positive" then
                    type_icon = "✓ "
                elseif mutator.type == "negative" then
                    type_icon = "✗ "
                elseif mutator.type == "neutral" then
                    type_icon = "◈ "
                elseif mutator.type == "event" then
                    type_icon = "★ "
                end
                content = content .. string.format("[%d] %s%s\n", i, type_icon, mutator.desc or "未知词条")
            end
            content = content .. "\n图例：✓有利 ✗挑战 ◈平衡 ★事件\n"

            -- 检查玩家的重投状态
            local player_reroll = self.owner and self.owner.components and self.owner.components.modplayer_reroll
            if player_reroll then
                if player_reroll:CanReroll() then
                    content = content .. "\n你今日可以重掷词条（每人每日一次）"
                else
                    content = content .. "\n你今日已重掷过词条"
                end
            else
                content = content .. "\n可使用 creroll 重掷词条（每人每日一次）"
            end
        else
            content = content .. "暂无词条信息"
        end
        
    elseif self.current_tab == "contracts" then
        content = "=== 合约任务 ===\n\n"
        if world_comp and world_comp.contracts then
            if #world_comp.contracts == 0 then
                content = content .. "暂无合约任务\n"
            else
                for i, contract in ipairs(world_comp.contracts) do
                    local status_icon = contract.completed and "✓" or "○"
                    local type_name = ""
                    if contract.type == "kill" then
                        type_name = "击杀"
                    elseif contract.type == "deliver" then
                        type_name = "交付"
                    elseif contract.type == "build" then
                        type_name = "建设"
                    end

                    local progress_text = string.format("%d/%d", contract.progress or 0, contract.goal or 0)
                    content = content .. string.format("[%d] %s %s：%s %s\n",
                        i, status_icon, type_name, contract.target_data.name or "未知", progress_text)

                    -- 显示奖励信息
                    local reward_text = string.format("奖励：%d恩惠", contract.reward_favor or 0)
                    if contract.reward_rep then
                        for faction, rep_amount in pairs(contract.reward_rep) do
                            reward_text = reward_text .. string.format("，%s声望+%d", faction, rep_amount)
                        end
                    end
                    content = content .. "    " .. reward_text .. "\n"

                    -- 如果是交付合约且未完成，显示交付提示
                    if contract.type == "deliver" and not contract.completed then
                        content = content .. string.format("    使用 cdeliver %d 交付物品\n", i)
                    end

                    content = content .. "\n"
                end

                content = content .. "图例：✓已完成 ○进行中\n"
                content = content .. "\n合约类型说明：\n"
                content = content .. "• 击杀：击败指定生物\n"
                content = content .. "• 交付：收集并交付指定物品\n"
                content = content .. "• 建设：建造指定结构\n"
            end
        else
            content = content .. "无法获取合约信息"
        end
        
    elseif self.current_tab == "boons" then
        -- 被动恩惠页面现在使用新的UI布局
        self:UpdateBoonsContent()
        return
        
    elseif self.current_tab == "reputation" then
        content = "=== 阵营声望 ===\n\n"
        if player_rep and player_rep.reps then
            content = content .. string.format("猪人阵营: %d\n", player_rep.reps.pig or 0)
            content = content .. string.format("猫狸阵营: %d\n", player_rep.reps.cat or 0)
            content = content .. string.format("兔人阵营: %d\n\n", player_rep.reps.bunny or 0)
            content = content .. "声望效果:\n"
            content = content .. "- 交易折扣\n"
            content = content .. "- 护航协助\n"
            content = content .. "- 隐藏商品"
        else
            content = content .. "无法获取声望信息"
        end
        
    elseif self.current_tab == "caravan" then
        content = "=== 商队信息 ===\n\n"
        if world_comp and world_comp.caravan then
            local next_day = world_comp.caravan.next_day or 0
            local current_day = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
            content = content .. string.format("下次商队: 第 %d 天\n", next_day)
            content = content .. string.format("当前天数: 第 %d 天\n\n", current_day)
            if next_day <= current_day then
                content = content .. "商队应该已经到达！\n"
            else
                content = content .. string.format("还需等待 %d 天\n", next_day - current_day)
            end
            content = content .. "\n商队功能:\n"
            content = content .. "- 回收冗余资源\n"
            content = content .. "- 兑换 Favor 代币\n"
            content = content .. "- 获取稀有蓝图"
        else
            content = content .. "无法获取商队信息"
        end
    end

    self.info_text:SetString(content)

    -- 兼容不同滚动控件的安全刷新/复位
    if self.info_text then
        if self.info_text.ScrollToTop then
            self.info_text:ScrollToTop()
        elseif self.info_text.ResetView then
            self.info_text:ResetView()
        end
    end
end

-- 新的被动恩惠UI更新方法（优化版）
function CaravanScreen:UpdateBoonsContent()
    -- 清除现有的文本显示
    self.info_text:SetString("")

    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    if not player_boons then
        self.info_text:SetString("无法获取被动恩惠信息")
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.boons_ui then
        self:CreateBoonsUI(player_boons)
    else
        -- 如果UI已存在，只更新内容
        self:UpdateBoonsUIContent(player_boons)
    end
end

-- 创建被动恩惠UI结构（只在第一次调用）
function CaravanScreen:CreateBoonsUI(player_boons)
    -- 创建被动恩惠UI容器
    self.boons_ui = self.info_area:AddChild(Widget("boons_ui"))
    self.boons_ui:SetPosition(0, 30)

    -- 创建头部信息区域
    self.boons_header = self.boons_ui:AddChild(Text(CHATFONT, 26, ""))
    SetColourSafe(self.boons_header, {1, 1, 0, 1})
    self.boons_header:SetPosition(0, 120)

    -- 添加操作说明
    local help_text = "鼠标悬停查看详情，点击进行操作"
    local help = self.boons_ui:AddChild(Text(CHATFONT, 20, help_text))
    SetColourSafe(help, {0.8, 0.8, 0.8, 1})
    help:SetPosition(0, 95)

    -- 创建技能树布局
    self:CreateSkillTree(player_boons)

    -- 添加状态说明
    self:CreateStatusLegend()

    -- 初始更新内容
    self:UpdateBoonsUIContent(player_boons)
end

-- 更新被动恩惠UI内容（增量更新）
function CaravanScreen:UpdateBoonsUIContent(player_boons)
    -- 更新头部信息
    if self.boons_header then
        local favor = player_boons.favor or 0
        local equipped_tbl = player_boons.equipped_boons or {}
        local max_equipped = player_boons.max_equipped or 2
        local header_text = string.format("Favor 代币: %d    已装备: %d/%d",
            favor, #equipped_tbl, max_equipped)
        self.boons_header:SetString(header_text)
    end

    -- 更新所有按钮状态
    if self.boon_buttons then
        for boon_id, button_data in pairs(self.boon_buttons) do
            self:UpdateBoonButtonState(boon_id, button_data, player_boons)
        end
    end
end

-- 创建技能树布局（优化版）
function CaravanScreen:CreateSkillTree(player_boons)
    local available_boons = player_boons:GetAvailableBoons()

    -- 初始化按钮管理器
    self.boon_buttons = {}

    -- 定义职业分类和位置（调整位置让布局更合理）
    local categories = {
        {name = "战士系", category = "warrior", pos = {-120, 40}, color = {1, 0.3, 0.3}},
        {name = "法师系", category = "mage", pos = {120, 40}, color = {0.3, 0.3, 1}},
        {name = "召唤师系", category = "summoner", pos = {-120, -80}, color = {0.8, 0.3, 1}},
        {name = "农民系", category = "farmer", pos = {120, -80}, color = {0.3, 1, 0.3}}
    }

    for _, cat_info in ipairs(categories) do
        self:CreateCategorySection(cat_info, available_boons, player_boons)
    end
end

-- 创建状态说明
function CaravanScreen:CreateStatusLegend()
    local legend_y = -140

    -- 状态说明标题
    local legend_title = self.boons_ui:AddChild(Text(CHATFONT, 22, "状态说明:"))
    SetColourSafe(legend_title, {1, 1, 1, 1})
    legend_title:SetPosition(0, legend_y)

    -- 状态图例
    local legends = {
        {text = "深灰色 = 未解锁", color = {0.5, 0.5, 0.5, 1}, pos = {-100, legend_y - 25}},
        {text = "黄色 = 已解锁", color = {0.8, 0.8, 0.2, 1}, pos = {0, legend_y - 25}},
        {text = "绿色 = 已装备", color = {0.2, 0.8, 0.2, 1}, pos = {100, legend_y - 25}}
    }

    for _, legend in ipairs(legends) do
        local legend_text = self.boons_ui:AddChild(Text(CHATFONT, 18, legend.text))
        SetColourSafe(legend_text, legend.color)
        legend_text:SetPosition(legend.pos[1], legend.pos[2])
    end
end

-- 创建职业分类区域
function CaravanScreen:CreateCategorySection(cat_info, available_boons, player_boons)
    -- 职业标题
    local title = self.boons_ui:AddChild(Text(CHATFONT, 24, cat_info.name))
    SetColourSafe(title, { (cat_info.color and cat_info.color[1]) or 1,
                           (cat_info.color and cat_info.color[2]) or 1,
                           (cat_info.color and cat_info.color[3]) or 1, 1 })
    title:SetPosition(cat_info.pos[1], cat_info.pos[2] + 40)

    -- 获取该职业的被动恩惠
    local category_boons = {}
    for boon_id, boon_def in pairs(available_boons) do
        if boon_def.category == cat_info.category then
            table.insert(category_boons, {id = boon_id, def = boon_def})
        end
    end

    -- 按费用排序
    table.sort(category_boons, function(a, b) return a.def.cost < b.def.cost end)

    -- 创建被动恩惠按钮
    for i, boon_info in ipairs(category_boons) do
        local y_offset = cat_info.pos[2] + 10 - (i - 1) * 35
        self:CreateBoonButton(boon_info.id, boon_info.def, cat_info.pos[1], y_offset, player_boons)
    end
end

-- 创建被动恩惠按钮（优化版）
function CaravanScreen:CreateBoonButton(boon_id, boon_def, x, y, player_boons)
    -- 创建按钮容器
    local button_container = self.boons_ui:AddChild(Widget("boon_button_" .. boon_id))
    button_container:SetPosition(x, y)

    -- 创建按钮
    local button = button_container:AddChild(TEMPLATES.StandardButton(
        function() self:OnBoonButtonClick(boon_id, boon_def, player_boons) end,
        boon_def.name,
        {150, 32}
    ))

    -- 添加Focus事件处理（饥荒UI标准，链式调用保留原有行为）
    local old_gain = button.OnGainFocus
    button.OnGainFocus = function(btn)
        if old_gain then old_gain(btn) end
        if btn.image then
            btn.image:SetTint(1.1, 1.1, 1.1, 1) -- 轻微高亮
        end
    end

    local old_lose = button.OnLoseFocus
    button.OnLoseFocus = function(btn)
        if old_lose then old_lose(btn) end
        -- 恢复原始颜色（在UpdateBoonButtonState中设置）
        local button_data = self.boon_buttons[boon_id]
        if button_data then
            self:UpdateBoonButtonState(boon_id, button_data, player_boons)
        end
    end

    -- 存储按钮数据以便后续更新
    local button_data = {
        container = button_container,
        button = button,
        boon_def = boon_def
    }
    self.boon_buttons[boon_id] = button_data

    -- 初始化按钮状态
    self:UpdateBoonButtonState(boon_id, button_data, player_boons)

    return button_container
end

-- 更新单个按钮状态（增量更新）
function CaravanScreen:UpdateBoonButtonState(boon_id, button_data, player_boons)
    local boon_def = button_data.boon_def
    local button = button_data.button

    -- 安全的表引用，避免索引nil
    local unlocked_tbl = player_boons.unlocked_boons or {}
    local equipped_tbl = player_boons.equipped_boons or {}

    -- 检查状态
    local is_unlocked = unlocked_tbl[boon_id] and unlocked_tbl[boon_id] > 0
    local is_equipped = false
    for _, equipped_id in ipairs(equipped_tbl) do
        if equipped_id == boon_id then
            is_equipped = true
            break
        end
    end

    -- 确定按钮颜色和文本
    local button_color = {0.5, 0.5, 0.5, 1} -- 默认灰色
    local text_color = {1, 1, 1, 1}
    local button_text = boon_def.name
    local status_text = ""

    if is_equipped then
        button_color = {0.2, 0.8, 0.2, 1} -- 绿色（已装备）
        text_color = {1, 1, 1, 1}
        button_text = "✓ " .. boon_def.name
        status_text = "已装备"
    elseif is_unlocked then
        button_color = {0.8, 0.8, 0.2, 1} -- 黄色（已解锁）
        text_color = {0, 0, 0, 1}
        status_text = "已解锁"
    else
        button_color = {0.3, 0.3, 0.3, 1} -- 深灰色（未解锁）
        text_color = {0.7, 0.7, 0.7, 1}
        status_text = "未解锁"
    end

    -- 更新按钮外观
    if button.image then
        button.image:SetTint(button_color[1], button_color[2], button_color[3], button_color[4])
    end
    if button.text then
        button.text:SetColour(text_color[1], text_color[2], text_color[3], text_color[4])
        button:SetText(button_text)
    end

    -- 更新tooltip（添加desc安全检查）
    local desc = boon_def.desc or "暂无描述"
    local tooltip_text = string.format("%s\n\n%s\n\n费用: %d 恩惠\n状态: %s",
        boon_def.name, desc, boon_def.cost, status_text)

    if is_equipped then
        tooltip_text = tooltip_text .. "\n\n点击卸下此被动恩惠"
    elseif is_unlocked then
        tooltip_text = tooltip_text .. "\n\n点击装备此被动恩惠"
        local max_equipped = player_boons.max_equipped or 2
        if #equipped_tbl >= max_equipped then
            tooltip_text = tooltip_text .. "\n注意: 装备槽已满"
        end
    else
        tooltip_text = tooltip_text .. "\n\n点击解锁此被动恩惠"
        local favor = player_boons.favor or 0
        if favor < boon_def.cost then
            tooltip_text = tooltip_text .. "\n注意: 恩惠不足"
        end
    end

    button:SetHoverText(tooltip_text)
end

-- 处理被动恩惠按钮点击（优化版）
function CaravanScreen:OnBoonButtonClick(boon_id, boon_def, player_boons)
    -- 安全的表引用，避免索引nil
    local unlocked_tbl = player_boons.unlocked_boons or {}
    local equipped_tbl = player_boons.equipped_boons or {}

    local is_unlocked = unlocked_tbl[boon_id] and unlocked_tbl[boon_id] > 0
    local is_equipped = false
    for _, equipped_id in ipairs(equipped_tbl) do
        if equipped_id == boon_id then
            is_equipped = true
            break
        end
    end

    local success = false

    if is_equipped then
        -- 卸下被动恩惠（使用组件自带的消息）
        success = player_boons:UnequipBoon(boon_id, false)
    elseif is_unlocked then
        -- 装备被动恩惠
        local max_equipped = player_boons.max_equipped or 2
        if #equipped_tbl >= max_equipped then
            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✗ 装备槽已满 (%d/%d)，请先卸下其他被动恩惠", #equipped_tbl, max_equipped))
            end
            return
        else
            success = player_boons:EquipBoon(boon_id, false)
        end
    else
        -- 解锁被动恩惠
        local favor = player_boons.favor or 0
        if favor >= boon_def.cost then
            success = player_boons:UnlockBoon(boon_id, false)
        else
            local needed = boon_def.cost - favor
            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✗ 恩惠不足！需要 %d 恩惠，还差 %d", boon_def.cost, needed))
            end
            return
        end
    end

    -- 只有成功操作才更新UI（使用增量更新）
    if success then
        self:UpdateBoonsUIContent(player_boons)
    end
end

function CaravanScreen:RefreshData()
    self:UpdateContent()
end

function CaravanScreen:Close()
    TheFrontEnd:PopScreen()
end

function CaravanScreen:OnControl(control, down)
    if CaravanScreen._base.OnControl(self, control, down) then
        return true
    end

    if not down and (control == CONTROL_CANCEL or control == CONTROL_MAP) then
        -- 播放关闭音效
        if TheFrontEnd and TheFrontEnd.GetSound and TheFrontEnd:GetSound() then
            local sound = TheFrontEnd:GetSound()
            if sound and sound.PlaySound then
                sound:PlaySound("dontstarve/HUD/click_move")
            end
        end
        self:Close()
        return true
    end

    return false
end

function CaravanScreen:GetHelpText()
    local controller_id = TheInput:GetControllerID()
    local t = {}
    table.insert(t, TheInput:GetLocalizedControl(controller_id, CONTROL_CANCEL) .. " " .. STRINGS.UI.HELP.BACK)
    return table.concat(t, "  ")
end

return CaravanScreen
