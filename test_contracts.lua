-- 测试合约系统
print("=== 测试合约系统 ===")

-- 模拟全局环境
local function CreateMockGlobals()
    GLOBAL = GLOBAL or {}
    GLOBAL.AllPlayers = {}
    GLOBAL.TheWorld = {
        state = { cycles = 1 },
        ismastersim = true,
        components = {},
        ListenForEvent = function(self, event, fn) 
            print("监听事件:", event)
        end,
        DoTaskInTime = function(self, time, fn)
            print("延迟任务:", time, "秒")
            fn()
        end
    }
    GLOBAL.TheNet = {
        GetIsServer = function() return true end,
        Announce = function(msg) print("公告:", msg) end
    }
    
    -- 模拟玩家
    local mock_player = {
        userid = "test_player",
        components = {
            talker = {
                Say = function(self, msg) print("玩家说:", msg) end
            },
            modplayer_boons = {
                favor = 0,
                AddFavor = function(self, amount)
                    self.favor = self.favor + amount
                    print("玩家获得星尘:", amount, "总计:", self.favor)
                end
            },
            modplayer_rep = {
                reps = {pig = 0, cat = 0, bunny = 0},
                AddReputation = function(self, faction, amount)
                    self.reps[faction] = self.reps[faction] + amount
                    print("玩家获得声望:", faction, amount, "总计:", self.reps[faction])
                end
            },
            inventory = {
                maxslots = 20,
                itemslots = {},
                RemoveItemBySlot = function(self, slot)
                    print("移除物品槽位:", slot)
                    self.itemslots[slot] = nil
                end
            }
        },
        IsValid = function() return true end,
        HasTag = function() return false end,
        ListenForEvent = function(self, event, fn)
            print("玩家监听事件:", event)
        end
    }
    
    table.insert(GLOBAL.AllPlayers, mock_player)
    return mock_player
end

-- 创建模拟环境
local mock_player = CreateMockGlobals()

-- 加载合约系统
local ModWorld = require("scripts/components/modworld_global")

-- 创建世界组件实例
local world_comp = ModWorld(GLOBAL.TheWorld)
GLOBAL.TheWorld.components.modworld_global = world_comp

print("\n=== 测试合约生成 ===")
world_comp:GenerateContracts()

print("\n=== 显示合约列表 ===")
world_comp:CmdShowContracts(mock_player)

print("\n=== 测试击杀合约 ===")
-- 模拟击杀蜘蛛
local mock_spider = {prefab = "spider"}
for i = 1, 5 do
    print("击杀蜘蛛", i)
    world_comp:OnEntityKilled(mock_spider, mock_player)
end

print("\n=== 测试建造合约 ===")
-- 模拟建造猪屋
local mock_pighouse = {prefab = "pighouse"}
print("建造猪屋")
world_comp:OnStructureBuilt(mock_pighouse, mock_player)

print("\n=== 测试交付合约 ===")
-- 模拟玩家背包中有金块
local mock_gold = {
    prefab = "goldnugget",
    components = {
        stackable = {
            StackSize = function() return 15 end,
            Get = function(self, amount) 
                print("从堆叠中取出:", amount)
                return amount
            end
        }
    }
}

-- 将金块放入玩家背包
mock_player.components.inventory.itemslots[1] = mock_gold

-- 尝试交付合约
print("尝试交付合约1")
world_comp:CmdDeliverContract(mock_player, "1")

print("\n=== 显示最终状态 ===")
world_comp:CmdShowContracts(mock_player)
print("玩家星尘:", mock_player.components.modplayer_boons.favor)
print("玩家猪人声望:", mock_player.components.modplayer_rep.reps.pig)

print("\n=== 测试完成 ===")
