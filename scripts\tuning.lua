-- Tuning file needs GLOBAL access

GLOBAL.TUNING.CARAVAN = {
    DIFFICULTY = GetModConfigData and GetModConfigData("difficulty") or "normal",
    MUTATOR_COUNT = GetModConfigData and GetModConfigData("mutator_count") or 2,
    CONTRACT_COUNT = GetModConfigData and GetModConfigData("contract_count") or 3,
    CONTRACTS_CONCURRENT = GetModConfigData and GetModConfigData("contracts_concurrent") or 4,
    FAVOR_MULT = GetModConfigData and GetModConfigData("favor_multiplier") or 1.0,
    CARAVAN_PERIOD_DAYS = GetModConfigData and GetModConfigData("caravan_period") or 7,
    ENABLE_BOSS_EVO = GetModConfigData and GetModConfigData("enable_boss_evo") ~= false,
    -- UI相关常量
    MAX_REPUTATION = 100, -- 最大声望值
    MAX_CARAVAN_WAIT_DAYS = 20, -- 最大商队等待天数
}

-- Difficulty presets (future use)
GLOBAL.TUNING.CARAVAN_DIFF = {
    easy = { favor_mult = 0.9 },
    normal = { favor_mult = 1.0 },
    hard = { favor_mult = 1.15 },
}

-- Strings (en/zh minimal)
GLOBAL.STRINGS.CARAVAN = {
    HELP = [[商旅巡游录可用指令(直接输入，无需斜杠):
chelp - 查看说明
cui - 打开/关闭UI界面 (推荐使用)
cmutators - 查看今日词条
creroll - 发起重掷词条投票（每人每日一次）
ccontracts - 查看当前合约
cdeliver <编号> - 交付合约物品（如: cdeliver 1）
cboons - 查看被动恩惠状态
cboon list - 查看所有被动恩惠
cboon unlock <ID> - 解锁被动恩惠
cboon equip <ID> - 装备被动恩惠
cboon unequip <ID> - 卸下被动恩惠
crep - 查看阵营声望
caravan - 查看商队信息
creapply - 重新应用词条效果到所有玩家（调试用）
cstatus - 检查词条系统状态（调试用）

被动恩惠ID（按职业分类）:
战士系: warrior_speed, warrior_damage, warrior_combo
法师系: mage_poison, mage_charge, mage_teleport
召唤师系: summon_spider, summon_pig, summon_boss
农民系: farmer_growth, farmer_harvest, farmer_blessing

控制台命令(按`键打开控制台):
c_rerolll() - 强制重掷词条，绕过每日限制（测试用）
c_mutators() - 查看当前词条（控制台版本）
c_findmutator("词条名") - 自动寻找特定词条

快捷键: 按 V 键打开/关闭UI界面]],
}

return GLOBAL.TUNING.CARAVAN
